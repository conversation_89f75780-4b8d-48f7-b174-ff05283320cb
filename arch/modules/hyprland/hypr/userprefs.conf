
# █░█ █▀ █▀▀ █▀█   █▀█ █▀█ █▀▀ █▀▀ █▀
# █▄█ ▄█ ██▄ █▀▄   █▀▀ █▀▄ ██▄ █▀░ ▄█

# Set your personal hyprland configuration here
# For a sample file, please refer to https://github.com/prasanthrangan/hyprdots/blob/main/Configs/.config/hypr/userprefs.t2



# █▀▄▀█ █▀█ █▄░█ █ ▀█▀ █▀█ █▀█
# █░▀░█ █▄█ █░▀█ █ ░█░ █▄█ █▀▄

monitor=DP-1,3440x1440@165,0x1440,1
monitor=DP-2,3440x1440@144,0x0,1
# monitor = ,preferred,auto,auto



# █░░ ▄▀█ █░█ █▄░█ █▀▀ █░█
# █▄▄ █▀█ █▄█ █░▀█ █▄▄ █▀█

exec-once = swayidle -w timeout 600 'swaylock' timeout 900 'hyprctl dispatch dpms off' resume 'hyprctl dispatch dpms on' # lock after 10 mins, sleep after 15 mins
exec-once = sway-audio-idle-inhibit
exec-once = solaar --window=hide
exec-once = dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP
exec-once = insync start
exec-once = blueman-tray
# exec-once = swayidle -w timeout 1200 'swaylock; hyprctl dispatch dpms off' resume 'hyprctl dispatch dpms on' timeout 1800 'systemctl suspend' # lock and sleep after 20 mins, suspend after 30 mins
# exec-once = libinput-gestures



# █▀▀ █▄░█ █░█
# ██▄ █░▀█ ▀▄▀

# Some default env vars.
env = XDG_CURRENT_DESKTOP,Hyprland
env = XDG_SESSION_TYPE,wayland
env = XDG_SESSION_DESKTOP,Hyprland
env = QT_QPA_PLATFORM,wayland
env = QT_STYLE_OVERRIDE,kvantum
env = QT_QPA_PLATFORMTHEME,qt5ct
env = QT_WAYLAND_DISABLE_WINDOWDECORATION,1
env = QT_AUTO_SCREEN_SCALE_FACTOR,1
env = MOZ_ENABLE_WAYLAND,1
env = EGL_PLATFORM,wayland
env = LIBGL_ALWAYS_SOFTWARE,1



# █▀▀ █▀▀ █▄░█ █▀▀ █▀█ ▄▀█ █░░
# █▄█ ██▄ █░▀█ ██▄ █▀▄ █▀█ █▄▄

general {
    no_focus_fallback = true
}



# █ █▄░█ █▀█ █░█ ▀█▀
# █ █░▀█ █▀▀ █▄█ ░█░

# For all categories, see https://wiki.hyprland.org/Configuring/Variables/
input {
    follow_mouse = 1

    touchpad {
        natural_scroll = no
    }

    sensitivity = 0.5 # -1.0 - 1.0, 0 means no modification.
    force_no_accel = 0
}



# █▄▀ █▀▀ █▄█ █▄▄ █ █▄░█ █▀▄ █ █▄░█ █▀▀ █▀
# █░█ ██▄ ░█░ █▄█ █ █░▀█ █▄▀ █ █░▀█ █▄█ ▄█

# bind = Ctrl+Alt+Shift, R, pass, ^(com\.obsproject\.Studio)$ # toggle obs screen recording // install obs flatpak
# bind = $mainMod+Shift, G, exec, pkill -x rofi || $scrPath/gamelauncher.sh # launch steam game launcher // install steam
# bind = $mainMod, Period, exec, emote # launch emoji selector // install emote
# bind = $mainMod+Alt, XF86MonBrightnessDown, exec, hyprshade on blue-light-filter # enable blue light filter // install hyprshade
# bind = $mainMod+Alt, XF86MonBrightnessUp, exec, hyprshade off # disable blue light filter // install hyprshade

# Unbind to prevent conflicts with base keybindings.conf
unbind = $mainMod+Shift, Left
unbind = $mainMod+Shift, Right
unbind = $mainMod+Shift, Up
unbind = $mainMod+Shift, Down
unbind = $mainMod+Shift+Ctrl, Left
unbind = $mainMod+Shift+Ctrl, Right
unbind = $mainMod+Shift+Ctrl, Up
unbind = $mainMod+Shift+Ctrl, Down
unbind = $mainMod SHIFT $CONTROL, left,Move activewindow to the right,exec, $moveactivewindow -30 0 || hyprctl dispatch movewindow l
unbind = $mainMod SHIFT $CONTROL, right,Move activewindow to the right,exec, $moveactivewindow 30 0 || hyprctl dispatch movewindow r
unbind = $mainMod SHIFT $CONTROL, up,Move activewindow to the right,exec, $moveactivewindow  0 -30 || hyprctl dispatch movewindow u
unbind = $mainMod SHIFT $CONTROL, down,Move activewindow to the right,exec, $moveactivewindow 0 30 || hyprctl dispatch movewindow d
unbind = , F10, exec, $scrPath/volumecontrol.sh -o m # toggle audio mute
unbind = , F11, exec, $scrPath/volumecontrol.sh -o d # decrease volume
unbind = , F12, exec, $scrPath/volumecontrol.sh -o i # increase volume

# Move active window around current workspace with mainMod + SHIFT + CTRL [←→↑↓]
$moveactivewindow=grep -q "true" <<< $(hyprctl activewindow -j | jq -r .floating) && hyprctl dispatch moveactive
binded = $mainMod SHIFT, left,Move activewindow to the right,exec, $moveactivewindow -30 0 || hyprctl dispatch movewindow l
binded = $mainMod SHIFT, right,Move activewindow to the right,exec, $moveactivewindow 30 0 || hyprctl dispatch movewindow r
binded = $mainMod SHIFT, up,Move activewindow to the right,exec, $moveactivewindow  0 -30 || hyprctl dispatch movewindow u
binded = $mainMod SHIFT, down,Move activewindow to the right,exec, $moveactivewindow 0 30 || hyprctl dispatch movewindow d

# Resize windows
binde = $mainMod+Shift+Ctrl, Right, resizeactive, 30 0
binde = $mainMod+Shift+Ctrl, Left, resizeactive, -30 0
binde = $mainMod+Shift+Ctrl, Up, resizeactive, 0 -30
binde = $mainMod+Shift+Ctrl, Down, resizeactive, 0 30

$browser = firefox-developer-edition

unbind = $mainMod, F, exec, $browser # launch web browser
bind = $mainMod, F, exec, $browser # launch web browser


# █░█░█ █ █▄░█ █▀▄ █▀█ █░█░█   █▀█ █░█ █░░ █▀▀ █▀
# ▀▄▀▄▀ █ █░▀█ █▄▀ █▄█ ▀▄▀▄▀   █▀▄ █▄█ █▄▄ ██▄ ▄█

# windowrulev2 = opacity 0.60 0.60,class:^(Steam)$
# windowrulev2 = opacity 0.60 0.60,class:^(steam)$
# windowrulev2 = opacity 0.60 0.60,class:^(steamwebhelper)$
# windowrulev2 = opacity 0.60 0.60,class:^(Spotify)$
# windowrulev2 = tile,class:^(vlc)$



# █░█ █▀▄▀█
# ▀▄▀ █░▀░█

# bind = Ctrl+Alt_L, V, submap, passthrough
# submap = passthrough
# bind = Ctrl+Alt_L, V, submap, reset
# submap = reset

