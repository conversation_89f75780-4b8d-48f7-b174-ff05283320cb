return {
  "nvim-treesitter/nvim-treesitter",
  event = { "BufReadPre", "BufNewFile" },
  build = ":TSUpdate",
  dependencies = {
    "windwp/nvim-ts-autotag",
  },
  config = function()
    -- import nvim-treesitter plugin
    local treesitter = require("nvim-treesitter.configs")

    -- configure treesitter
    treesitter.setup({ -- enable syntax highlighting
      highlight = {
        enable = true,
      },
      -- enable indentation
      indent = { enable = true },
      -- enable autotagging (w/ nvim-ts-autotag plugin)
      autotag = {
        enable = true,
      },
      auto_install = true,
      -- ensure these language parsers are installed
      ensure_installed = {
        -- "json",
        -- "javascript",
        -- "typescript",
        -- "tsx",
        -- "yaml",
        -- "html",
        -- "css",
        -- "markdown",
        -- "markdown_inline",
        -- "bash",
        -- "lua",
        -- "vim",
        -- "dockerfile",
        -- "gitignore",
        -- "query",
        -- "vimdoc",
        -- "c",
        -- "sql",
        -- "diff",
        -- "c_sharp",
        -- "python",
      },
      incremental_selection = {
        enable = true,
        keymaps = {
          init_selection = "<leader>fi",
          node_incremental = "<leader>fn",
          scope_incremental = false,
          node_decremental = "<leader>fb",
        },
      },
    })
  end,
}
