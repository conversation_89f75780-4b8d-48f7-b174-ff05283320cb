# Tmux Plugin Manager (TPM) configuration
# prefix + I to install plugins
# prefix + U to update plugins
# prefix + alt + u to remove plugins

# Reload tmux configuration
unbind r
bind R source-file ~/.config/tmux/tmux.conf \; display-message "tmux.conf reloaded"
bind r command-prompt "rename-window %%"

# Pane synchronization toggle with feedback
bind C-s setw synchronize-panes \; run-shell 'tmux display-message "Toggled pane synchronization: #{?pane_synchronized,on,off}"'

# Prefix and mouse settings
set -g prefix C-Space
set -g mouse on
set -g base-index 1
set -g renumber-windows on

# Window navigation with Shift + Left/Right
bind -n S-Left  previous-window
bind -n S-Right next-window

# vi key movement for copy/paste mode
set-window-option -g mode-keys vi
bind -T copy-mode-vi v send-keys -X begin-selection
bind -T copy-mode-vi y send-keys -X copy-pipe-and-cancel "xclip -i -selection clipboard"

# Sensible tmux options
set -s escape-time 0                # Reduce vim mode switching delay
set -g history-limit 50000          # Increase scrollback buffer size
set -g display-time 4000            # Increase message display duration
set -g status-interval 5            # Refresh status intervals more often
set -g default-terminal "screen-256color"
set -g status-keys vi               # Use vim-style keys
set -g focus-events on              # Enable focus events
setw -g aggressive-resize on        # Resize panes when the window changes

# Yazi Settings
set -g allow-passthrough on
set -ga update-environment TERM
set -ga update-environment TERM_PROGRAM

# Plugins
set -g @plugin 'tmux-plugins/tpm'
set -g @plugin 'christoomey/vim-tmux-navigator'
set -g @plugin 'alexwforsythe/tmux-which-key'
set -g @plugin 'sainnhe/tmux-fzf'
set -g @plugin 'omerxx/tmux-sessionx'
set -g @plugin 'tmux-plugins/tmux-online-status'
set -g @plugin 'tmux-plugins/tmux-battery'
set -g @plugin 'catppuccin/tmux'

# Sessionx Options
set -g @sessionx-auto-accept 'off'
set -g @sessionx-bind 'o'
set -g @sessionx-window-height '85%'
set -g @sessionx-window-width '75%'
set -g @sessionx-zoxide-mode 'on'
set -g @sessionx-custom-paths-subdirectories 'false'
set -g @sessionx-filter-current 'false'

# Configure Catppuccin
set -g @catppuccin_flavor "mocha"
set -g @catppuccin_status_background "none"
set -g @catppuccin_window_status_style "none"
set -g @catppuccin_pane_status_enabled "off"
set -g @catppuccin_pane_border_status "off"

# Configure Online
set -g @online_icon "ok"
set -g @offline_icon "nok"

# status left look and feel
set -g status-left-length 100
set -g status-left ""
set -ga status-left "#{?client_prefix,#{#[bg=#{@thm_red},fg=#{@thm_bg},bold]  #S },#{#[bg=#{@thm_bg},fg=#{@thm_green}]  #S }}"
set -ga status-left "#[bg=#{@thm_bg},fg=#{@thm_overlay_0},none]│"
set -ga status-left "#[bg=#{@thm_bg},fg=#{@thm_maroon}]  #{pane_current_command} "
set -ga status-left "#[bg=#{@thm_bg},fg=#{@thm_overlay_0},none]│"
set -ga status-left "#[bg=#{@thm_bg},fg=#{@thm_blue}]  #{=/-32/...:#{s|$USER|~|:#{b:pane_current_path}}} "
set -ga status-left "#[bg=#{@thm_bg},fg=#{@thm_overlay_0},none]#{?window_zoomed_flag,│,}"
set -ga status-left "#[bg=#{@thm_bg},fg=#{@thm_yellow}]#{?window_zoomed_flag,  zoom ,}"

# status right look and feel
set -g status-right-length 100
set -g status-right ""
set -ga status-right "#{?#{e|>=:10,#{battery_percentage}},#{#[bg=#{@thm_red},fg=#{@thm_bg}]},#{#[bg=#{@thm_bg},fg=#{@thm_pink}]}} #{battery_icon} #{battery_percentage} "
set -ga status-right "#[bg=#{@thm_bg},fg=#{@thm_overlay_0}, none]│"
set -ga status-right "#[bg=#{@thm_bg}]#{?#{==:#{online_status},ok},#[fg=#{@thm_mauve}] 󰖩 on ,#[fg=#{@thm_red},bold]#[reverse] 󰖪 off }"
set -ga status-right "#[bg=#{@thm_bg},fg=#{@thm_overlay_0}, none]│"
set -ga status-right "#[bg=#{@thm_bg},fg=#{@thm_blue}] 󰭦 %d-%b-%y #[fg=#{@thm_yellow}]󰅐 %H:%M "


# bootstrap tpm
if "test ! -d ~/.tmux/plugins/tpm" \
   "run 'git clone https://github.com/tmux-plugins/tpm ~/.tmux/plugins/tpm && ~/.tmux/plugins/tpm/bin/install_plugins'"

# Initialize TMUX plugin manager (keep this line at the very bottom of tmux.conf)
run '~/.tmux/plugins/tpm/tpm'

# Configure Tmux
set -g status-position top
set -g status-style "bg=#{@thm_bg}"
set -g status-justify "absolute-centre"

# pane border look and feel
setw -g pane-border-status top
setw -g pane-border-format ""
setw -g pane-active-border-style "bg=#{@thm_bg},fg=#{@thm_overlay_0}"
setw -g pane-border-style "bg=#{@thm_bg},fg=#{@thm_surface_0}"
setw -g pane-border-lines single

# window look and feel
set -wg automatic-rename on
set -g automatic-rename-format "Window"

set -g window-status-format " #I#{?#{!=:#{window_name},Window},: #W,} "
set -g window-status-style "bg=#{@thm_bg},fg=#{@thm_rosewater}"
set -g window-status-last-style "bg=#{@thm_bg},fg=#{@thm_peach}"
set -g window-status-activity-style "bg=#{@thm_red},fg=#{@thm_bg}"
set -g window-status-bell-style "bg=#{@thm_red},fg=#{@thm_bg},bold"
set -gF window-status-separator "#[bg=#{@thm_bg},fg=#{@thm_overlay_0}]│"

set -g window-status-current-format " #I#{?#{!=:#{window_name},Window},: #W,} "
set -g window-status-current-style "bg=#{@thm_peach},fg=#{@thm_bg},bold"

# Pane border styling
setw -g pane-active-border-style 'fg=magenta,bg=default'
setw -g pane-border-style 'fg=brightblack,bg=default'
