# =============================================================================
# Git Configuration - Modern Git Setup
# =============================================================================
# This config includes user settings and sensible defaults for development
# User-specific settings are loaded from ~/.gitconfig_default_user
# =============================================================================

[include]
	path = ~/.gitconfig_default_user

# =============================================================================
# Core Settings
# =============================================================================
[init]
	defaultBranch = main

[core]
	editor = nvim
	autocrlf = false
	pager = bat --style=plain
	excludesfile = ~/.gitignore_global

# =============================================================================
# Push/Pull Behavior
# =============================================================================
[push]
	autoSetupRemote = true
	default = simple

[pull]
	rebase = true

# =============================================================================
# Branch and Merge Settings
# =============================================================================
[branch]
	autosetupmerge = always
	autosetuprebase = always

[merge]
	conflictstyle = diff3

# =============================================================================
# Helpful Aliases
# =============================================================================
[alias]
	# Status and info
	st = status -s
	br = branch
	co = checkout

	# Logging
	lg = log --oneline --graph --decorate --all
	last = log -1 HEAD

	# Staging
	unstage = reset HEAD --

	# Diff
	df = diff
	dc = diff --cached

	# Quick commit
	cm = commit -m

	# Undo last commit (keep changes)
	undo = reset --soft HEAD~1

# =============================================================================
# Color Settings
# =============================================================================
[color]
	ui = auto
	branch = auto
	diff = auto
	status = auto

[color "branch"]
	current = yellow reverse
	local = yellow
	remote = green

[color "diff"]
	meta = yellow bold
	frag = magenta bold
	old = red bold
	new = green bold

[color "status"]
	added = yellow
	changed = green
	untracked = cyan
