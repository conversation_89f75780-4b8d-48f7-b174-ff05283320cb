# =============================================================================
# Global Git Ignore - Common Files to Ignore Across All Projects
# =============================================================================
# This file contains patterns for files that should be ignored in all git repos
# It's referenced in .gitconfig as core.excludesfile
# =============================================================================

# =============================================================================
# Operating System Files
# =============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Editor and IDE Files
# =============================================================================

# Vim
*.swp
*.swo
*~
.viminfo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# VSCode
.vscode/
*.code-workspace

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# =============================================================================
# Development Tools
# =============================================================================

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
.env
pip-log.txt
pip-delete-this-directory.txt

# Rust
target/
Cargo.lock

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# =============================================================================
# Build and Temporary Files
# =============================================================================

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# =============================================================================
# Security and Secrets
# =============================================================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# SSH keys
id_rsa
id_dsa
id_ecdsa
id_ed25519
*.pem

# GPG keys
*.gpg
*.asc

# =============================================================================
# Backup and Archive Files
# =============================================================================

*.bak
*.backup
*.old
*.orig
*.tmp
*.temp
*.cache

# Compressed files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# =============================================================================
# Database Files
# =============================================================================

*.sqlite
*.sqlite3
*.db
*.database
