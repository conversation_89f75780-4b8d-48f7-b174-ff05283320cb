#!/usr/bin/bash

# =============================================================================
# Git Module - Version Control and GitHub Integration
# =============================================================================
# Dependencies: none (base module)
# Packages: git, github-cli, openssh
# Configs: .gitconfig, user settings
# =============================================================================

# Module metadata (for future tooling)
readonly MODULE_NAME="git"
readonly MODULE_VERSION="1.0.0"
readonly MODULE_DESCRIPTION="Git version control with GitHub CLI integration"

# Package definitions
readonly GIT_PACKAGES=(
    "git"           # Core git
    "github-cli"    # GitHub CLI tool
    "openssh"       # SSH client for git operations
)

# Configuration paths
readonly GIT_CONFIG_SOURCE="$MODULES_DIR/git/config/.gitconfig"
readonly GIT_CONFIG_DEST="$HOME/.gitconfig"
readonly GIT_USER_CONFIG="$HOME/.gitconfig_default_user"
readonly GIT_IGNORE_SOURCE="$MODULES_DIR/git/config/.gitignore_global"
readonly GIT_IGNORE_DEST="$HOME/.gitignore_global"

# =============================================================================
# Main Installation Function
# =============================================================================
function install_git() {
    echo "📦 Installing Git packages..."
    install_packages "${GIT_PACKAGES[@]}"

    echo "⚙️  Configuring Git..."
    configure_git

    echo "🔑 Setting up Git user..."
    setup_git_user

    echo "✅ Git module installation complete"
}

# =============================================================================
# Configuration Functions
# =============================================================================
function configure_git() {
    echo "🔗 Linking Git configuration..."
    symlink_config "$GIT_CONFIG_SOURCE" "$GIT_CONFIG_DEST"

    echo "🚫 Linking global gitignore..."
    symlink_config "$GIT_IGNORE_SOURCE" "$GIT_IGNORE_DEST"
}

function setup_git_user() {
    validate_git_environment
    create_user_config
    verify_git_setup
}

function validate_git_environment() {
    if [[ -z "$GIT_NAME" ]]; then
        echo "❌ Error: GIT_NAME is not set in .env"
        echo "💡 Please add: GIT_NAME=\"Your Name\""
        return 1
    fi

    if [[ -z "$GIT_EMAIL" ]]; then
        echo "❌ Error: GIT_EMAIL is not set in .env"
        echo "💡 Please add: GIT_EMAIL=\"<EMAIL>\""
        return 1
    fi
}

function create_user_config() {
    echo "👤 Creating user configuration..."
    cat > "$GIT_USER_CONFIG" << EOF
[user]
    name = $GIT_NAME
    email = $GIT_EMAIL
EOF
    echo "✅ Git user configured: $GIT_NAME <$GIT_EMAIL>"
}

function verify_git_setup() {
    if command -v git &>/dev/null; then
        local git_user=$(git config user.name 2>/dev/null || echo "Not set")
        local git_email=$(git config user.email 2>/dev/null || echo "Not set")
        echo "🔍 Git verification:"
        echo "   Name: $git_user"
        echo "   Email: $git_email"
        echo "   Context: $CONTEXT"
    fi
}

# =============================================================================
# Optional: Module-specific utilities (for future enhancement)
# =============================================================================
function setup_git_ssh() {
    # Future enhancement: SSH key setup
    echo "🔑 SSH key setup not implemented yet"
}

function configure_git_aliases() {
    # Future enhancement: Custom git aliases
    echo "🔧 Git aliases not implemented yet"
}
