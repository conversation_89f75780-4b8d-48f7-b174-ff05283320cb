# Git Module

Modern Git configuration with GitHub CLI integration and sensible defaults.

## Overview

This module sets up a comprehensive Git environment with:
- Core Git version control
- GitHub CLI for repository management
- SSH client for secure Git operations
- Optimized configuration with useful aliases
- Global gitignore for common files

## Dependencies

- **None** - This is a base module

## Packages Installed

- `git` - Core Git version control system
- `github-cli` - GitHub command-line interface
- `openssh` - SSH client for Git operations

## Configuration Files

### `.gitconfig`
Main Git configuration with:
- Modern defaults (main branch, rebase on pull)
- Useful aliases (st, lg, cm, etc.)
- Color configuration
- Editor set to nvim
- Pager set to bat

### `.gitignore_global`
Global gitignore patterns for:
- Operating system files (macOS, Windows, Linux)
- Editor/IDE files (VSCode, Vim, JetBrains)
- Development tools (Node.js, Python, Rust, Go)
- Build artifacts and temporary files
- Security-sensitive files

### `.gitconfig_default_user`
User-specific configuration created from environment variables:
- `GIT_NAME` - Your full name
- `GIT_EMAIL` - Your email address

## Environment Variables Required

Add these to your `.env` file:

```bash
GIT_NAME="Your Full Name"
GIT_EMAIL="<EMAIL>"
```

## Useful Git Aliases

After installation, you can use these shortcuts:

```bash
git st          # git status -s
git lg          # git log --oneline --graph --decorate --all
git cm "msg"    # git commit -m "msg"
git co branch   # git checkout branch
git br          # git branch
git df          # git diff
git dc          # git diff --cached
git unstage     # git reset HEAD --
git undo        # git reset --soft HEAD~1 (undo last commit, keep changes)
```

## Verification

After installation, verify your setup:

```bash
git config user.name    # Should show your name
git config user.email   # Should show your email
git config core.editor  # Should show nvim
```

## Future Enhancements

Planned features for future versions:
- Automated SSH key setup
- Additional Git aliases
- Git hooks configuration
- GPG signing setup
