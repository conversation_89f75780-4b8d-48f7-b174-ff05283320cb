#!/usr/bin/bash

# Test script to demonstrate dependency resolution without actually installing packages
# This simulates the install process to show how dependencies work

set -e

# Get the directory of the current script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MODULES_DIR="$SCRIPT_DIR/modules"

# Source common functions and dependencies
source "$MODULES_DIR/common.sh"
source "$MODULES_DIR/dependencies.sh"

# Set up mock environment variables for testing
export ENVIRONMENT="arch"
export CONTEXT="personal"
export GIT_NAME="Test User"
export GIT_EMAIL="<EMAIL>"

# Mock the package installation function for testing
function install_packages() {
  local packages=("$@")
  echo "   📦 Would install packages: ${packages[*]}"
}

# Mock the ensure_package_installed function
function ensure_package_installed() {
  local package="$1"
  echo "   ✓ Would ensure $package is installed"
}

# Mock yay installation
function ensure_yay_installed() {
  echo "   ✓ yay would be ensured to be installed"
}

# Mock symlink creation
function symlink_config() {
  local source_path="$1"
  local dest_path="$2"
  echo "   🔗 Would create symlink: $source_path → $dest_path"
}

# Mock shell change to avoid password prompt
function chsh() {
  echo "   🐚 Would change shell to: $2"
}

# Mock git clone to avoid actual cloning
function git() {
  echo "   📥 Would run git command: git $*"
}

# Mock ya pack command
function ya() {
  echo "   📦 Would run ya command: ya $*"
}

echo "🧪 Dry run test of dependency-aware installation..."
echo "=================================================="
echo

# Test with a realistic set of modules that have dependencies
TEST_MODULES=("neovim" "tmux" "shell" "python")

echo "🎯 Testing with modules: ${TEST_MODULES[*]}"
echo

# Resolve dependencies
echo "🔍 Resolving module dependencies..."
RESOLVED_MODULES=($(resolve_dependencies "${TEST_MODULES[@]}"))

echo "📋 Installation order: ${RESOLVED_MODULES[*]}"
echo

# Simulate installation
for module in "${RESOLVED_MODULES[@]}"; do
  echo "====================="
  echo "Processing $module..."
  echo "====================="
  
  # Check if module script exists
  MODULE_SCRIPT="$MODULES_DIR/$module/$module.sh"
  if [[ -f "$MODULE_SCRIPT" ]]; then
    # Source and run the install function
    source "$MODULE_SCRIPT"
    echo "   🚀 Running install_$module function..."
    "install_$module" 2>/dev/null || echo "   ⚠️  Module function completed (some errors expected in dry run)"
    echo "   ✅ Module '$module' processed"
  else
    echo "   ❌ Module script not found: $MODULE_SCRIPT"
  fi
  echo
done

echo "🎉 Dry run completed successfully!"
echo
echo "Key observations:"
echo "- Dependencies were resolved automatically"
echo "- Modules were processed in the correct order"
echo "- Each module's install function was called"
echo
echo "To run the actual installation:"
echo "1. Edit install.sh and uncomment desired modules"
echo "2. Run: ./install.sh"
